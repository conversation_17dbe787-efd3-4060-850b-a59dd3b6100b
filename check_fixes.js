const fs = require('fs');

const content = fs.readFileSync('src/pages/submission/QuantitativeNewSubmission.js', 'utf8');
const lines = content.split('\n');

console.log('Checking for undefined variable usage...\n');

let foundIssues = false;

// Check for checkHardcoded usage
const checkHardcodedUsage = lines.filter((line, i) => 
    line.includes('checkHardcoded(') && 
    !line.includes('const checkHardcoded') && 
    !line.includes('//') &&
    line.trim() !== ''
);

if (checkHardcodedUsage.length > 0) {
    console.log('✓ checkHardcoded function is being used properly');
} else {
    console.log('⚠ No usage of checkHardcoded found');
}

// Check for getStructuredResponse usage
const getStructuredResponseUsage = lines.filter((line, i) => 
    line.includes('getStructuredResponse(') && 
    !line.includes('const getStructuredResponse') && 
    !line.includes('//') &&
    line.trim() !== ''
);

if (getStructuredResponseUsage.length > 0) {
    console.log('✓ getStructuredResponse function is being used properly');
} else {
    console.log('⚠ No usage of getStructuredResponse found');
}

// Check for checkResponse_275 usage
const checkResponse275Usage = lines.filter((line, i) => 
    line.includes('checkResponse_275(') && 
    !line.includes('const checkResponse_275') && 
    !line.includes('//') &&
    line.trim() !== ''
);

if (checkResponse275Usage.length > 0) {
    console.log('✓ checkResponse_275 function is being used properly');
} else {
    console.log('⚠ No usage of checkResponse_275 found');
}

// Check for comparisonFn usage
const comparisonFnUsage = lines.filter((line, i) => 
    line.includes('comparisonFn') && 
    !line.includes('const comparisonFn') && 
    !line.includes('//') &&
    line.trim() !== ''
);

if (comparisonFnUsage.length > 0) {
    console.log('✓ comparisonFn is being used properly');
} else {
    console.log('⚠ No usage of comparisonFn found');
}

// Check for function definitions
const checkHardcodedDef = lines.find(line => line.includes('const checkHardcoded = '));
const getStructuredResponseDef = lines.find(line => line.includes('const getStructuredResponse = '));
const checkResponse275Def = lines.find(line => line.includes('const checkResponse_275 = '));
const comparisonFnDef = lines.find(line => line.includes('const comparisonFn = '));

console.log('\nFunction definitions:');
console.log('✓ checkHardcoded defined:', !!checkHardcodedDef);
console.log('✓ getStructuredResponse defined:', !!getStructuredResponseDef);
console.log('✓ checkResponse_275 defined:', !!checkResponse275Def);
console.log('✓ comparisonFn defined:', !!comparisonFnDef);

console.log('\nAll fixes appear to be in place! 🎉');
