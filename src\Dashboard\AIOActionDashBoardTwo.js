import React, { useEffect, useState } from "react";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";
import { getDate, getFiscalYearsFromStartDate, getReportingFiscalYearByReportingperiod, getRPLuxon, getRPTextFormat } from "../components/BGHF/helper";
import { useSelector } from "react-redux";
import { DateTime } from "luxon";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { TabMenu } from "primereact/tabmenu";
import { Badge } from "primereact/badge";
import { MultiSelect } from "primereact/multiselect";
import { Tag } from "primereact/tag";
import { InputText } from "primereact/inputtext";
import { Calendar } from "primereact/calendar";
import { Dialog } from "primereact/dialog";
import { TabView, TabPanel } from 'primereact/tabview';
import { Tooltip } from "primereact/tooltip";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import Swal from "sweetalert2";

const AIOActionDashBoardTwo = () => {

    const [actions, setActions] = useState([])
    const [reload, setReload] = useState(false)
    const [archived, setArchived] = useState([])
    const [dcfList, setDCFList] = useState([])

    const [sourcedialog, setSourceDialog] = useState(false)
    const [selectedIndicator, setSelectedIndicator] = useState(null)

    const [actionsbk, setActionsBk] = useState([])
    const [archivedbk, setArchivedBk] = useState([])
    const [activeindex, setActiveIndex] = useState(0)
    const [load, setLoad] = useState(true)
    const [search, setSearch] = useState({ action: '', archive: '' })
    const [archiveFilters, setArchiveFilters] = useState({
        category: null,
        status: null,
        coverage: null,
        searchTitle: ''
    })


    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const allRoles = useSelector((state) => state.user.allRoles);
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);


    // Only the 4 required form types
    const formType = [
        { name: 'Quantitative Submission', id: 1 },
        { name: 'Quantitative Review', id: 3 },
        { name: 'Qualitative Submission', id: 8 },
        { name: 'Qualitative Consolidation', id: 10 },
    ]

    const yrOptions = getFiscalYearsFromStartDate(admin_data?.start_date, fymonth);
    const [selectedYear, setSelectedYear] = useState(yrOptions[0]);

    // Filtered data state
    const [filteredActions, setFilteredActions] = useState([]);
    const [filteredArchived, setFilteredArchived] = useState([]);

    useEffect(() => {
        APIServices.get(API.UserProfile_Edit(login_data.id)).then((res) => {
            if (res.data.blocked !== 1) {
                renderData()
            } else {
                Swal.fire({
                    title: "Your account has been archived by the administrator and is no longer active. Please contact support for assistance",
                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    if (result.isConfirmed) {
                        localStorage.removeItem('token')
                        window.location.href = '/';
                    }
                })
            }
        })
    }, [])

    useEffect(() => {
        if (actions.length) {
            console.log(actions)
        }
    }, [actions])

    useEffect(() => {
        const handleFocus = () => {
            if (localStorage.getItem("reloadaio")) {
                renderData();
                localStorage.removeItem("reloadaio");
            }
        };

        window.addEventListener("focus", handleFocus);
        return () => {
            window.removeEventListener("focus", handleFocus);
        };
    }, []);

    useEffect(() => {
        if (reload) {
            renderData()
            setReload(false)
        }
    }, [reload])

    // Filter archived data based on filters
    useEffect(() => {
        let filtered = [...archivedbk];

        // Filter by category
        if (archiveFilters.category && archiveFilters.category.length > 0) {
            filtered = filtered.filter(item => archiveFilters.category.includes(item.formType));
        }

        // Filter by status
        if (archiveFilters.status && archiveFilters.status.length > 0) {
            filtered = filtered.filter(item => archiveFilters.status.includes(item.currentStatus));
        }

        // Filter by coverage
        if (archiveFilters.coverage && archiveFilters.coverage.length > 0) {
            filtered = filtered.filter(item => archiveFilters.coverage.includes(item.coverage));
        }

        // Filter by title search
        if (archiveFilters.searchTitle && archiveFilters.searchTitle.trim() !== '') {
            const searchTerm = archiveFilters.searchTitle.toLowerCase();
            filtered = filtered.filter(item =>
                item.title && item.title.toLowerCase().includes(searchTerm)
            );
        }

        setFilteredArchived(filtered);
    }, [archiveFilters, archivedbk]);
    const RowFilterTemplate = (options, obj) => {
        console.log(Array.from(new Set(actions.map((i) => i[obj]))))
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(actions.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                panelClassName="hidefilter"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
  const fomrTypeFilterTemplate = (options, obj) => {
        let activeFormType = Array.from(new Set(actionsbk.map(i => i.formType).filter(x => x)))
        return (
            <MultiSelect
                value={options.value}
                options={formType.filter(x => activeFormType.includes(x.id))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                optionLabel="name"
                optionValue="id"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    function getMonthsBetweenExcludingFuture(start_date, end_date, frequency, filterYear, fymonth) {
        let fyStartDate, fyEndDate;
        // to show annual 3 months earlier
        if (filterYear === 0) {
            fyStartDate = start_date ? DateTime.fromISO(start_date, { zone: "utc" }).toLocal() : null;
            fyEndDate = end_date ? DateTime.fromISO(end_date, { zone: "utc" }).toLocal() : DateTime.utc().toLocal();
        } else {
            // Fiscal Year: Apr (fymonth) of the previous year to Mar (fymonth - 1) of filterYear
            fyStartDate = DateTime.fromObject({ year: filterYear - 1, month: fymonth, day: 1 });
            fyEndDate = DateTime.fromObject({ year: filterYear, month: fymonth - 1, day: 31 });
        }

        // Convert user-defined start/end dates
        const userStartDate = start_date ? DateTime.fromISO(start_date, { zone: "utc" }).toLocal() : fyStartDate;
        const userEndDate = end_date ? DateTime.fromISO(end_date, { zone: "utc" }).toLocal() : fyEndDate;

        // Adjust final start and end dates within fiscal year boundaries
        const finalStartDate = userStartDate < fyStartDate ? fyStartDate : userStartDate;
        const finalEndDate = userEndDate > fyEndDate ? fyEndDate : userEndDate;

        // Calculate last 3 months start date
        const lastThreeMonthsStart = finalEndDate.minus({ months: 3 });

        // Get current date to filter out current and future months
        const currentDate = DateTime.local();
        const currentMonthStart = currentDate.startOf('month');

        const months = [];
        let currentMonth = finalStartDate;

        while (currentMonth <= finalEndDate) {
            const periodEnd = currentMonth.plus({ months: frequency - 1 });

            // Check if the period is in the past (exclude current and future months)
            const isPastPeriod = periodEnd < currentMonthStart;

            if (frequency === 12) {
                // For annual frequency
            }

            // ✅ ALLOW period only when it's within the original conditions AND it's in the past
            if (isPastPeriod &&
                ((frequency === 12 && finalEndDate >= lastThreeMonthsStart) ||
                    (frequency !== 12 && periodEnd >= finalStartDate && periodEnd <= finalEndDate))) {

                if (frequency === 12) {
                    months.push(currentMonth.toFormat("LLL-yyyy") + " to " + periodEnd.toFormat("LLL-yyyy"));
                } else if (frequency === 1) {
                    months.push(currentMonth.toFormat("LLL-yyyy"));
                } else {
                    months.push(currentMonth.toFormat("LLL-yyyy") + " to " + periodEnd.toFormat("LLL-yyyy"));
                }
            }

            // Move to next period
            currentMonth = currentMonth.plus({ months: frequency });
        }

        return months;
    }



    function getOverdueDays(monthString) {
        console.log(monthString);
        const [startMonth, endMonth] = monthString.split(" to ");

        const month = endMonth ? endMonth : startMonth;
        const [monthValue, year] = month.split("-");
        const endOfMonth = DateTime.fromObject({
            year: parseInt(year),
            month: DateTime.fromFormat(monthValue, "LLL").month,
        }).endOf("month");
        const currentDate = DateTime.local();
        console.log(month, endOfMonth.diff(currentDate, "days").days);
        return endOfMonth.diff(currentDate, "days").days;
    }

    const getCoverageText = (rowData, rawsitelist) => {
        let text = "";

        if (rowData.level === 0) {
            text = "Corporate";
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.locationId
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.locationId;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.level === 3) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.locationId;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };

    const renderData = () => {
        setLoad(true)
        let assignment = []
        let archived_assignment = []

        let uriString = {
            include: [
                {
                    relation: "locationTwos",
                    scope: { include: [{ relation: "locationThrees" }] },
                },
            ],
        };

        // Simplified API calls - only what's needed for the 4 form types
        const promise0 = APIServices.get(API.DCF_Title_Only);
        const promise1 = APIServices.get(API.QN_Submit_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify({ include: [{ relation: 'dcf', scope: { fields: { id: true, title: true, type: true, tags: true, categoryId: true, categoryAltId: true, subCategoryDpIds: true, subCategoryOrder: true, calculationDpIds: true, suffix: true } } }], fields: { id: true, reject: true, type: true, edit: true, dcfId: true, entityUserAssId: true, entityAssId: true, tier0_id: true, tier1_id: true, tier2_id: true, tier3_id: true, reporting_period: true, locationId: true, level: true, frequency: true, self: true, last_modified_on: true, approved_on: true, reviewed_on: true, submitted_on: true } }))}`);
        const promise2 = APIServices.get(API.LocationOne_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`);
        const promise3 = APIServices.get(API.DCF_Entity_UP(admin_data.id));
        const promise4 = APIServices.get(API.DCF_User_Assignment_UP(admin_data.id));
        const promise5 = APIServices.post(API.GetAssignedQualitative, { userId: login_data.id, userProfileId: admin_data.id });
        const promise6 = APIServices.post(API.GetAssignedQualitative_Consolidator, { userId: login_data.id, userProfileId: admin_data.id });

        Promise.all([
            promise0, promise1, promise2, promise3, promise4, promise5, promise6
        ]).then((values) => {
            console.log(values);
            let dcf_list = values[0].data,
                quantitative_submitted = values[1].data,
                rawSiteData = values[2].data,
                quantitative_entity_list = values[3].data,
                quantitative_ass_list = values[4].data,
                qualitative_ass_list = values[5].data,
                qualitative_consolidator_list = values[6].data;

            let qlAssignment = [], qlConsolidatorAssignment = [];
            setDCFList(dcf_list);

            // Shape site data like in original
            const shapedSite = rawSiteData
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees &&
                                locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);



            if (qualitative_ass_list.length && qualitative_ass_list?.filter(x => x.reporter_ids.includes(login_data.id)).length) {
                qlAssignment = [{
                    formType: 8, coverage: 'Corporate',
                    name: "Add/Update Qualitative Responses", title: "Add/Update Qualitative Responses", dueDate: null, currentStatus: 'Update', timeLine: "Ongoing", statusCode: 6
                }]
            }

            if (qualitative_consolidator_list?.status) {
                qlConsolidatorAssignment = [{
                    formType: 10, coverage: 'Corporate',
                    name: "Add/Update Qualitative Consolidate Responses", title: "Add/Update Qualitative Consolidate Responses", dueDate: null, currentStatus: 'Update', timeLine: "Ongoing", statusCode: 6
                }]
            }
            // Process Quantitative Submissions (formType: 1) and Reviews (formType: 3)
            for (const item of quantitative_ass_list.filter(x => x.reporter_ids.includes(login_data.id))) {
                let months = getMonthsBetweenExcludingFuture(
                    item.start_date,
                    item.end_date,
                    item.frequency === 4 ? 12 : item.frequency === 5 ? 6 : item.frequency,
                    0,
                    fymonth
                );
                let entityName = getCoverageText(item, shapedSite)

                for (const item2 of months) {

                    let found = quantitative_submitted.find(x => x.dcfId === item.dcfId && x.entityUserAssId === item.id &&
                        x.entityAssId === item.entityAssId &&
                        x.tier0_id === item.tier0_id &&
                        x.tier1_id === item.tier1_id &&
                        x.tier2_id === item.tier2_id &&
                        x.tier3_id === item.tier3_id && getRPTextFormat(x.reporting_period) === item2)
                    if (found) {
                        let type = found.type;
                        let reject = found.reject;
                        let status = type === 0 && (!reject)
                            ? 0
                            : type === 0 && (reject === 1 || reject === 2)
                                ? 1
                                : type === 1 && reject === 1
                                    ? 2
                                    : type === 1
                                        ? 3
                                        : type === 2
                                            ? 4
                                            : type === 3
                                                ? 5
                                                : null;

                        if (status === 0 || status === 1) {
                            // Use getOverdueDays result to determine status logic
                            const overdueDays = getOverdueDays(item2);

                            if (overdueDays >= 0) {
                                status = 99; // This won't occur with getMonthsBetweenExcludingFuture
                            } else if (overdueDays >= -10) {
                                // Within 10 days overdue: Use status 6
                                status = 6;
                            } else {
                                // More than 10 days overdue: Use status 7
                                status = 7;
                            }
                        }

                        let submittedDate = '';
                        if (status === 2 || status === 3 || !item.reviewer_ids.length) {
                            submittedDate = found?.submitted_on;
                        } else if (status === 4) {
                            submittedDate = found?.reviewed_on;
                        } else if (status === 5) {
                            submittedDate = found?.approved_on;
                        } else if (status === 6 || status === 7) {
                            submittedDate = found?.last_modified_on;
                        }

                        // Add to appropriate list based on status
                        const assignmentItem = {
                            formSubmittedOn: submittedDate,
                            coverage: entityName,
                            data: found,
                            dcfId: item.dcfId,
                            formType: 1, // Always 1 for quantitative submissions
                            standard: item.standard,
                            frequency: item.frequency,
                            entityAssId: item.entityAssId,
                            entityUserAssId: item.id,
                            reporting_period: item2,
                            self: item.reviewer_ids.length ? false : true,
                            locationId: item.locationId,
                            level: item.level,
                            name: item.dcf?.title,
                            title: item.dcf?.title + ' for the reporting period of ' + item2 + ' for ' + entityName || '',
                            dueDate: getDueDate(item2),
                            currentStatus: !type && reject ? "Returned" : status === 3 ? 'Under Review' : status === 4 ? 'Under Approval' : status === 5 ? 'Approved' : 'Draft',
                            timeLine: status === 6 ? 'Ongoing' : status === 7 ? "Overdue" : status === 9 ? "Duesoon" : "Upcoming",
                            statusCode: status
                        };


                        assignment.push(assignmentItem);

                    } else if (!found) {
                        // Use getOverdueDays result to determine status logic
                        const overdueDays = getOverdueDays(item2);

                        let stat;
                        if (overdueDays >= 0) {
                            stat = 100; // This won't occur with getMonthsBetweenExcludingFuture
                        } else if (overdueDays >= -10) {
                            // Within 10 days overdue: Use status 6
                            stat = 6;
                        } else {
                            // More than 10 days overdue: Use status 7
                            stat = 7;
                        }

                        assignment.push({
                            coverage: entityName,
                            dcfId: item.dcfId,
                            standard: item.standard,
                            formType: 1, // Always 1 for quantitative submissions
                            frequency: item.frequency,
                            entityAssId: item.entityAssId,
                            entityUserAssId: item.id,
                            reporting_period: item2,
                            self: item.reviewer_ids.length ? false : true,
                            locationId: item.locationId,
                            level: item.level,
                            name: item.dcf?.title,
                            title: item.dcf?.title + ' for the reporting period of ' + item2 + ' for ' + entityName || '',
                            dueDate: getDueDate(item2),
                            currentStatus: 'Not Started',
                            timeLine: stat === 6 ? 'Ongoing' : stat === 7 ? "Overdue" : stat === 9 ? "Duesoon" : "Upcoming",
                            statusCode: stat
                        });
                    }
                }
            }

            // Process Quantitative Reviews (formType: 3) - Separate processing for reviewers
            quantitative_submitted.forEach((item) => {
                let filtered_qn_ass_index = quantitative_ass_list.findIndex(
                    (x) => x.dcfId === item.dcfId && x.id === item.entityUserAssId
                );
                if (filtered_qn_ass_index !== -1) {
                    if (quantitative_ass_list[filtered_qn_ass_index].reviewer_ids.includes(login_data.id)) {
                        let status = item.type === 2 ? 4 : item.type === 1 ? getOverdueDays(getRPTextFormat(item.reporting_period)) >= -15 ? 6 : 7 : 5;
                        console.log(item)
                        if (status) {
                            assignment.push({
                                coverage: getCoverageText(item, shapedSite),
                                data: item,
                                dcfId: item.dcfId,
                                formType: 3, // Always 3 for quantitative reviews
                                frequency: quantitative_ass_list[filtered_qn_ass_index].frequency,
                                entityAssId: item.entityAssId,
                                entityUserAssId: item.entityUserAssId,
                                reporting_period: item.reporting_period,
                                self: item?.self ? true : false,
                                locationId: item.locationId,
                                level: item.level,
                                name: item.dcf?.title,
                                title: item.dcf?.title + ' for the reporting period of ' + getRPTextFormat(item.reporting_period) || '',
                                dueDate: getDueDate(getRPTextFormat(item.reporting_period), 14),
                                currentStatus: status === 4 ? 'Under Approval' : 'Under Review',
                                timeLine: status === 6 ? 'Ongoing' : status === 7 ? "Overdue" : status === 9 ? "Duesoon" : 'Upcoming',
                                statusCode: status
                            });
                        }
                    }
                }
            });


            console.log('Assignment:', assignment);
            console.log('Archived:', archived_assignment);

            // Filter active items - include items that need action
            const activeItems = assignment.filter(x =>
                [6, 7, 9].includes(x.statusCode)
            );

            // Filter archived items - include completed/approved items
            const archivedItems = assignment.filter(x =>
                [3, 4, 5, 0].includes(x.statusCode)
            );
            console.log('Active Items:', activeItems);

            setActions([...activeItems, ...qlAssignment, ...qlConsolidatorAssignment]);
            setActionsBk([...activeItems, ...qlAssignment, ...qlConsolidatorAssignment]);
            setArchived([...archivedItems]);
            setArchivedBk([...archivedItems]);
            setFilteredArchived([...archivedItems]); // Initialize filtered archived data
            setLoad(false);
        }).catch((error) => {
            console.error('Error loading data:', error);
            setLoad(false);
        });
    }

    const getDueDate = (dateStr, day) => {
        const [from, to] = dateStr.split(" to ");
        if (to) {
            return DateTime.fromFormat(to, "LLL-yyyy").plus({ month: 1 }).startOf('month').plus({ day: day || 0 }).toFormat('dd-LLL-yyyy');
        }
        return DateTime.fromFormat(from, "LLL-yyyy").plus({ month: 1 }).startOf('month').plus({ day: day || 0 }).toFormat('dd-LLL-yyyy')
    };

    // Action template for the 4 form types
    const actionTemplate = (rowData) => {




        switch (rowData.formType) {

            case 1:


                return <div className="table-link-clickable" onClick={() => {
                    if (rowData?.data) {
                        window.open(
                            window.location.origin +
                            "/data_input_past/" +
                            rowData.dcfId +
                            "/" +
                            rowData.data.id
                        );
                    } else {
                        window.open(
                            window.location.origin + "/data_input_newer/" + rowData.dcfId
                        ).sessionStorage.setItem('newer', JSON.stringify({ ...rowData, company_id: admin_data.id }))
                    }
                }} > {'Submit ' + rowData.title} </div>

            case 3:
                return <div className="table-link-clickable" onClick={() => {
                    window.open(
                        window.location.origin +
                        "/data_input_reviewer/" +
                        rowData?.dcfId +
                        "/" +
                        rowData?.data?.id
                    );
                }} > {'Review ' + rowData.title} </div>

            case 8:
                return <div className="table-link-clickable" onClick={() => window.open(window.location.origin + '/qualitative')} >{rowData.title} </div>
            case 10:
                return <div className="table-link-clickable" onClick={() => window.open(window.location.origin + '/consolidate_qualitative')}  >  {rowData.title} </div>
            default:
                return <></>;
        }
    }

    // Archive action template
    const actionArchiveTemplate = (rowData) => {
        switch (rowData.formType) {
            case 1: // Quantitative Submission
                return <div className="table-link-clickable" onClick={() => {
                    window.open(
                        window.location.origin +
                        "/data_input_view/" +
                        rowData.dcfId +
                        "/" +
                        rowData?.data?.id
                    );
                }} >{'View ' + rowData.title} </div>

            case 3: // Quantitative Review
                return <div className="table-link-clickable" onClick={() => {
                    window.open(
                        window.location.origin +
                        "/data_input_view/" +
                        rowData?.dcfId +
                        "/" +
                        rowData?.data?.id
                    );
                }} >{rowData.title} </div>

            case 8: // Qualitative Submission
                return <div className="table-link-clickable" onClick={() => window.open(window.location.origin + '/qualitative')} >{rowData.title} </div>

            case 10: // Qualitative Consolidation
                return <div className="table-link-clickable" onClick={() => window.open(window.location.origin + '/consolidate_qualitative')}  >  {rowData.title} </div>

            default:
                return <></>;
        }
    }

    // Form type template
    const formTypeTemplate = (rowData) => {
        const formTypeObj = formType.find(x => x.id === rowData.formType);
        return formTypeObj ? <Tag severity="info" value={formTypeObj.name} /> : <></>;
    };

    // Current status template
    const currentStatusTemplate = (rowData) => {
        let severity = "info";
        switch (rowData.currentStatus) {
            case "Approved":
                severity = "success";
                break;
            case "Under Review":
            case "Under Approval":
                severity = "warning";
                break;
            case "Returned":
                severity = "danger";
                break;
            default:
                severity = "info";
        }
        return <Tag severity={severity} value={rowData.currentStatus} />;
    };

    // Due date template
    const dueDateBodyTemplate = (rowData) => {
        console.log(rowData, ' R D ')
        if (!rowData || !rowData.dueDate) {
            return '';
        }

        // Attempt to parse the date
        const parsedDate = new Date(rowData.dueDate);

        // If date is invalid, just return the original value
        if (isNaN(parsedDate)) {
            return rowData.dueDate;
        }

        // Format the date as DD-MM-YYYY
        const day = String(parsedDate.getDate()).padStart(2, '0');
        const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
        const year = parsedDate.getFullYear();

        return `${day}-${month}-${year}`;
    };

    // Submitted date template
    const getSubmittedDate = (rowData) => {
        if (!rowData) return '';

        console.log(rowData)
        // For other types of submissions
        if (rowData.formSubmittedOn) {

            return getDate(rowData.formSubmittedOn);

        }

        return '-';
    };

    // Timeline template
    const timelineTemplate = (rowData) => {
        switch (rowData.statusCode) {
            case 6:
                return <Badge severity="info" value="Ongoing" />;
            case 7:
                return <Badge severity="danger" value="Overdue" />;
            case 9:
                return <Badge severity="info" value="Due Soon" />;
            default:
                return <></>
        }
    };

    // Filter templates with search functionality

    const statusFilterTemplate = (options) => {
        const statusOptions = [
            { label: 'Draft', value: 'Draft' },
            { label: 'Under Review', value: 'Under Review' },
            { label: 'Under Approval', value: 'Under Approval' },
            { label: 'Approved', value: 'Approved' },
            { label: 'Returned', value: 'Returned' },
            { label: 'Not Started', value: 'Not Started' },
            { label: 'Update', value: 'Update' },
            { label: 'Completed', value: 'Completed' }
        ];

        return (
            <MultiSelect
                value={options.value}
                options={statusOptions}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Select Status"
                optionLabel="label"
                optionValue="value"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
                filter
                filterPlaceholder="Search status..."
                showClear
            />
        );
    };

    // Archive filter handlers
    const handleArchiveCategoryFilter = (selectedCategories) => {
        setArchiveFilters(prev => ({ ...prev, category: selectedCategories }));
    };

    const handleArchiveStatusFilter = (selectedStatuses) => {
        setArchiveFilters(prev => ({ ...prev, status: selectedStatuses }));
    };

    const handleArchiveCoverageFilter = (selectedCoverages) => {
        setArchiveFilters(prev => ({ ...prev, coverage: selectedCoverages }));
    };

    const handleArchiveTitleSearch = (searchTerm) => {
        setArchiveFilters(prev => ({ ...prev, searchTitle: searchTerm }));
    };

    // Get unique values for archive filters
    const getUniqueArchiveCategories = () => {
        const categories = Array.from(new Set(archivedbk.map(item => item.formType).filter(x => x)));
        return formType.filter(ft => categories.includes(ft.id));
    };

    const getUniqueArchiveStatuses = () => {
        return Array.from(new Set(archivedbk.map(item => item.currentStatus).filter(x => x)));
    };

    const getUniqueArchiveCoverages = () => {
        return Array.from(new Set(archivedbk.map(item => item.coverage).filter(x => x)));
    };



    // Tab menu items
    const items = [
        { label: `Action Required (${actions.length})`, icon: 'pi pi-fw pi-home' },
        { label: `Archive (${filteredArchived.length})`, icon: 'pi pi-fw pi-calendar' }
    ];

    return (
        <div>
            <div className="col-12 flex align-items-center" >
                <span className="text-big-one"> Hello  {login_data?.role === 'clientadmin' ? login_data?.information?.companyname : login_data?.information?.empname} ! </span>   <span className="ml-1">{`<${login_data.email}>`} </span>
            </div>
            <div
                className="col-12 "
                style={{
                    justifyContent: "flex-start",
                }}
            >
                <label className="text-big-one clr-navy flex fw-7 flex justify-content-start">

                    My Actions
                </label>
                <label className="fs-14 clr-navy flex  justify-content-start">
                    This page lists your tasks for the Sustainability Reporting process, including submissions, reviews, as applicable. Please complete each action to keep the reporting process on track.
                </label>
            </div>
            <div >
                <TabMenu model={items} activeIndex={activeindex} onTabChange={(e) => setActiveIndex(e.index)} />

                {activeindex === 0 &&
                    <div className="mt-3">
                        <DataTable
                            loading={load}
                            scrollable
                            className="h-500"
                            showGridlines
                            paginator
                            rows={10}
                            filters={{
                                coverage: { value: null, matchMode: "in" },
                                timeLine: { value: null, matchMode: "in" },
                                formType: { value: null, matchMode: "in" },
                                currentStatus: { value: null, matchMode: "in" }
                            }}
                            rowsPerPageOptions={[10, 25, 50, 100]}
                            value={actions}
                        >
                             <Column header="Timeline" field='statusCode' body={timelineTemplate} />
                           

                            <Column header="Category" field='formType'
                                filter
                                body={formTypeTemplate}
                              filterElement={fomrTypeFilterTemplate}
                                showFilterMatchModes={false}
                            />
                             <Column header="Coverage" field='coverage'
                                filter filterElement={(options) =>
                                    RowFilterTemplate(options, "coverage")
                                }
                                filterPlaceholder="Search coverage..."
                                showFilterMatchModes={false} />
                            <Column header="Required Action" field='title'
                                body={actionTemplate}

                                filterPlaceholder="Search action..."
                            />
                            <Column header="Due Date" field='dueDate' body={dueDateBodyTemplate} />
                            <Column header="Submitted On" field="formSubmittedOn" body={getSubmittedDate} />
                            <Column header="Current Status" field='currentStatus'
                                filter
                                body={currentStatusTemplate}
                                filterElement={(options) =>
                                    RowFilterTemplate(options, "currentStatus")
                                }
                                showFilterMatchModes={false}
                            />
                           
                        </DataTable>
                    </div>
                }

                {activeindex === 1 &&
                    <div className="mt-3">
                        {/* Archive Filters */}
                        <div className="mb-3 p-3 border-round surface-50">
                            <div className="grid">
                                <div className="col-12 md:col-3">
                                    <label className="block text-sm font-medium mb-2">Search by Title</label>
                                    <InputText
                                        value={archiveFilters.searchTitle}
                                        onChange={(e) => handleArchiveTitleSearch(e.target.value)}
                                        placeholder="Search by title..."
                                        className="w-full"
                                    />
                                </div>
                                <div className="col-12 md:col-3">
                                    <label className="block text-sm font-medium mb-2">Category</label>
                                    <MultiSelect
                                        value={archiveFilters.category}
                                        options={getUniqueArchiveCategories()}
                                        onChange={(e) => handleArchiveCategoryFilter(e.value)}
                                        placeholder="Select Categories"
                                        optionLabel="name"
                                        optionValue="id"
                                        className="w-full"
                                        maxSelectedLabels={2}
                                        showClear
                                    />
                                </div>
                                <div className="col-12 md:col-3">
                                    <label className="block text-sm font-medium mb-2">Status</label>
                                    <MultiSelect
                                        value={archiveFilters.status}
                                        options={getUniqueArchiveStatuses().map(status => ({ label: status, value: status }))}
                                        onChange={(e) => handleArchiveStatusFilter(e.value)}
                                        placeholder="Select Statuses"
                                        optionLabel="label"
                                        optionValue="value"
                                        className="w-full"
                                        maxSelectedLabels={2}
                                        showClear
                                    />
                                </div>
                                <div className="col-12 md:col-3">
                                    <label className="block text-sm font-medium mb-2">Coverage</label>
                                    <MultiSelect
                                        value={archiveFilters.coverage}
                                        options={getUniqueArchiveCoverages().map(coverage => ({ label: coverage, value: coverage }))}
                                        onChange={(e) => handleArchiveCoverageFilter(e.value)}
                                        placeholder="Select Coverage"
                                        optionLabel="label"
                                        optionValue="value"
                                        className="w-full"
                                        maxSelectedLabels={2}
                                        showClear
                                    />
                                </div>
                            </div>
                        </div>

                        <DataTable
                            loading={load}
                            scrollable
                            className="h-500"
                            showGridlines
                            paginator
                            rows={10}
                            rowsPerPageOptions={[10, 25, 50, 100]}
                            value={filteredArchived}
                        >
                            <Column header="Coverage" field='coverage' />
                            <Column header="Category" field='formType' body={formTypeTemplate} />
                            <Column header="Action" field='title' body={actionArchiveTemplate} />
                            <Column header="Due Date" field='dueDate' body={dueDateBodyTemplate} />
                            <Column header="Submitted On" field="formSubmittedOn" body={getSubmittedDate} />
                            <Column header="Current Status" field='currentStatus' body={currentStatusTemplate} />
                        </DataTable>
                    </div>
                }
            </div>
        </div>
    );
};

export default AIOActionDashBoardTwo;
